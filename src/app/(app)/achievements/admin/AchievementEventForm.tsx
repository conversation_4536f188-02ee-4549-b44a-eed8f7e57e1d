"use client";

import React, { useState } from "react";
import <PERSON>ton from "~/component/button";

export type EventFormDTO = {
  id: string;
  code: string;
  title: string;
  description: string | null;
  startsAt: string | null;
  endsAt: string | null;
  badgeImageUrl: string | null;
  visible: boolean;
  legacy: boolean;

};

interface Props {
  initialEvent: EventFormDTO;
}

export default function AchievementEventForm({ initialEvent }: Props) {
  const [event, setEvent] = useState<EventFormDTO>(initialEvent);
  const [saving, setSaving] = useState(false);


  async function handleMakeLegacy() {
    if (!event) return;
    if (!confirm("Dieses Event wirklich als Legacy markieren?")) return;

    try {
      const res = await fetch(`/api/achievments/admin/events/${event.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ legacy: true }),
      });
      if (!res.ok) throw new Error(await res.text());
      alert("Event wurde zu Legacy verschoben.");
    } catch (e: any) {
      alert(e.message || "Aktion fehlgeschlagen");
    }
  }


  async function handleSave() {
    setSaving(true);
    try {
      const res = await fetch(`/api/achievments/admin/events/${event.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(event),
      });
      if (!res.ok) throw new Error(await res.text());
      alert("Event gespeichert");
    } catch (e: any) {
      alert(e.message || "Speichern fehlgeschlagen");
    } finally {
      setSaving(false);
    }
  }

  return (
    <form
      className="space-y-4"
      onSubmit={(e) => {
        e.preventDefault();
        void handleSave();
      }}
    >
      <label className="block text-sm">
        <span className="text-gray-700">Titel</span>
        <input
          className="w-full rounded-md border px-2 py-1"
          value={event.title}
          onChange={(e) => setEvent({ ...event, title: e.target.value })}
        />
      </label>

      <label className="block text-sm">
        <span className="text-gray-700">Beschreibung</span>
        <textarea
          className="w-full rounded-md border px-2 py-1"
          value={event.description ?? ""}
          onChange={(e) => setEvent({ ...event, description: e.target.value })}
        />
      </label>

      <div className="flex gap-2">
        <label className="flex-1 text-sm">
          <span className="text-gray-700">Start</span>
          <input
            type="datetime-local"
            className="w-full rounded-md border px-2 py-1"
            value={event.startsAt ?? ""}
            onChange={(e) => setEvent({ ...event, startsAt: e.target.value })}
          />
        </label>
        <label className="flex-1 text-sm">
          <span className="text-gray-700">Ende</span>
          <input
            type="datetime-local"
            className="w-full rounded-md border px-2 py-1"
            value={event.endsAt ?? ""}
            onChange={(e) => setEvent({ ...event, endsAt: e.target.value })}
          />
        </label>
      </div>

      <label className="block text-sm flex items-center gap-2">
        <input
          type="checkbox"
          checked={event.visible}
          onChange={(e) => setEvent({ ...event, visible: e.target.checked })}
        />
        <span>Event sichtbar</span>
      </label>

      <div className="flex justify-between">
        {!event.legacy && (
          <Button
            type="button"
            onClick={handleMakeLegacy}
            className="bg-red-600 text-white"
          >
            Zu Legacy machen
          </Button>
        )}
        <Button type="submit" disabled={saving}>
          {saving ? "Speichere…" : "Speichern"}
        </Button>
      </div>

    </form>
  );
}
