import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";
import UserCdrGraph from "~/app/(app)/testDashboard/components/UserCdrGraph";
import type { Cdr as GraphCdr } from "~/types/CardHolderDashboardTypes/UserCdrGraphTypes";
import Card from "~/component/card";
import {NewsPost} from "~/types/CardHolderDashboardTypes/NewsListTypes";
import NewsList from "~/app/(app)/testDashboard/components/NewsList";

import RealtimeWidget from "~/app/(app)/component/RealtimeWidget";
import { MdElectricBolt } from "react-icons/md";
import { FaMoneyBillTransfer } from "react-icons/fa6";

export default async function Page() {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return <div>Bitte einloggen.</div>;
  }

  // Holt ALLE CDRs des Cardholders und formt sie in den Graph-Typ um
  const getBilableCdrsForCardHolder = async (): Promise<GraphCdr[]> => {
    const userId = session.user.id;

    const cards = await prisma.eMPCard.findMany({
      where: { userId },
      select: { physicalCard: { select: { uid: true } } },
    });

    const tokenUids = cards
      .map(c => c.physicalCard?.uid)
      .filter((uid): uid is string => !!uid);

    if (tokenUids.length === 0) return [];

    const rows = await prisma.cdr.findMany({
      where: {
        Authentication_ID: { in: tokenUids },
        billable: true,
      },
      orderBy: { Start_datetime: "asc" },
      select: {
        CDR_ID: true,
        Start_datetime: true,
        End_datetime: true,
        Volume: true,
        Calculated_Cost: true,
        EnergyCosts: true,
        Charging_Time_Cost: true,
        Parking_Time_Cost: true,
        LocalStart_datetime: true,
        LocalEnd_datetime: true,
      },
    });

    // Map auf den Typ, den UserCdrGraph erwartet
    const mapped: GraphCdr[] = rows.map(r => ({
      CDR_ID: r.CDR_ID,
      Start_datetime: r.Start_datetime,          // darf Date oder string sein (dein Typ erlaubt beides)
      End_datetime: r.End_datetime,
      LocalStart_datetime: r.LocalStart_datetime ?? null,
      LocalEnd_datetime: r.LocalEnd_datetime ?? null,
      Volume: r.Volume ?? null,
      Calculated_Cost: r.Calculated_Cost ?? null,
      EnergyCosts: r.EnergyCosts ?? null,
      Charging_Time_Cost: r.Charging_Time_Cost ?? null,
      Parking_Time_Cost: r.Parking_Time_Cost ?? null,
    }));

    return mapped;
  };



  //<PowerMixChart
    //             data={[
    //               { carrier: "Wind", percent: 33 },
    //               { carrier: "Solar", percent: 14 },
    //               { carrier: "Biomasse", percent: 9 },
    //               { carrier: "Wasserkraft", percent: 5 },
    //               { carrier: "Kohle", percent: 29 },
    //               { carrier: "Gas", percent: 9 },
    //               { carrier: "Sonstiges", percent: 1 },
    //             ]}
    //             asOf={new Date()}
    //             intensity_g_per_kwh={325}
    //           />

  const posts: NewsPost[] = [
    {
      title: "Förderprogramm „Ladeinfrastruktur vor Ort“ – Aufhebung der 50 kW-Drosselung nach 12 Monaten offiziell bestätigt",
      href: "https://www.eulektro.de/blog/news-2/forderprogramm-ladeinfrastruktur-vor-ort-aufhebung-der-50-kw-drosselung-nach-12-monaten-offiziell-bestatigt-23",
      image: "https://www.eulektro.de/web/image/7791-955caeb2/Blogbeitrag%20%27F%C3%B6rderprogramm%20%E2%80%9ELadeinfrastruktur%20vor%20Ort%E2%80%9C%20%E2%80%93%20Aufhebung%20der%2050%20kW-Drosselung%20nach%2012%20Monaten%20offiziell%20best%C3%A4tigt%27%20cover%20image.webp",
      date: "2025-08-25",
      subtitle: "Betreiber von geförderten DC-Ladepunkten können ab sofort nach nur 12 Monaten die 50 kW-Drosselung aufheben – ganz ohne Verlust der Förderung. Das sorgt für kürzere Ladezeiten und mehr Attraktivität am Standort.",
    },
    {
      title: "So senkst du AC-Kosten",
      href: "https://andere-seite.de/blog/ac-kosten",
      date: "2025-08-12",
    },
  ];


  const allCdrsFromUser = await getBilableCdrsForCardHolder();
  // --- Helper: robustes Date + Monatsschnitt ---
  const asDate = (d: Date | string | null | undefined): Date | null =>
    !d ? null : (d instanceof Date ? d : new Date(d));

  const startOfMonth = (y: number, m: number) => new Date(y, m, 1, 0, 0, 0, 0);
  const endOfMonthOpen = (y: number, m: number) => new Date(y, m + 1, 1, 0, 0, 0, 0); // [start, end)

  const now = new Date();
  const thisStart = startOfMonth(now.getFullYear(), now.getMonth());
  const thisEndOpen = endOfMonthOpen(now.getFullYear(), now.getMonth());

  const prevMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  const prevStart = startOfMonth(prevMonth.getFullYear(), prevMonth.getMonth());
  const prevEndOpen = endOfMonthOpen(prevMonth.getFullYear(), prevMonth.getMonth());

  const cdrDate = (c: GraphCdr): Date | null =>
    asDate(c.LocalStart_datetime ?? c.Start_datetime);

  const inRange = (d: Date | null, start: Date, endOpen: Date) =>
    !!d && d >= start && d < endOpen;

  const costOf = (c: GraphCdr): number => {
    const calc = c.Calculated_Cost;
    if (calc != null) return Number(calc);
    const e = Number(c.EnergyCosts ?? 0);
    const ct = Number(c.Charging_Time_Cost ?? 0);
    const pt = Number(c.Parking_Time_Cost ?? 0);
    return e + ct + pt;
  };

  const volOf = (c: GraphCdr): number => Number(c.Volume ?? 0);

// --- Filter je Monat ---
  const thisMonthCdrs = allCdrsFromUser.filter(c => inRange(cdrDate(c), thisStart, thisEndOpen));
  const prevMonthCdrs = allCdrsFromUser.filter(c => inRange(cdrDate(c), prevStart, prevEndOpen));

// --- Deine Konstanten ---
  const kWhThisMonth  = thisMonthCdrs.reduce((s, c) => s + volOf(c), 0);
  const kWhLastMonth  = prevMonthCdrs.reduce((s, c) => s + volOf(c), 0);


  const costThisMonth = thisMonthCdrs.reduce((s, c) => s + costOf(c), 0);
  const costLastMonth = prevMonthCdrs.reduce((s, c) => s + costOf(c), 0);

// Beispiel: in eine Card/Widget stecken oder weiterreichen
 console.log({ kWhThisMonth, kWhLastMonth, costThisMonth, costLastMonth });


  // Vermutlich willst du die Graph-Ansicht FÜR Card Holder anzeigen:
  if (session.user.role === Role.CARD_HOLDER) {
    return (

      <div >

        <UserCdrGraph cdrs={allCdrsFromUser} />

        <div className="mt-6 grid grid-cols-8 gap-4">
          <RealtimeWidget
            caption={"Geladene Kwh dieses Monats"}
            loading={false}
            icon={<MdElectricBolt size={21} />}
            primaryTitle={"Geladene Kwh dieses Monats"}
            primaryValue={kWhThisMonth}
            secondaryTitle={"KwhLastMonth"}
            secondaryValue={kWhLastMonth}
            className="col-span-2" // nimmt 1/6
          />
          <RealtimeWidget
            caption={"Angefallene Kosten in diesem Monat"}
            loading={false}
            icon={<FaMoneyBillTransfer size={21} />
            }
            primaryTitle={"KostenThisMonth"}
            primaryValue={costThisMonth}
            secondaryTitle={"KostenLastMonth"}
            secondaryValue={costLastMonth}
            className="col-span-2" // nimmt 1/6
          />

          <Card header_left={"News"} className="col-span-4"> {/* nimmt 4/6 */}
            <NewsList posts={posts} openInNewTab />
          </Card>
        </div>



      </div>



    );
  }

  // Optional: Fallback für andere Rollen
  return <div>Keine Berechtigung für diese Ansicht.</div>;
}
