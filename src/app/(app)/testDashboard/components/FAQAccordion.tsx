"use client";

import React, { useEffect, useMemo, useRef, useState } from "react";
import Accordion from "~/app/(app)/component/Accordion"; // passe den Importpfad an
import type { FaqItem, FaqProps } from "~/types/CardHolderDashboardTypes/FAQTypes";



// ——— Helper ———
const slug = (s: string) =>
  s
    .toLowerCase()
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "")
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/(^-|-$)+/g, "");

const toId = (item: FaqItem, index: number) => item.id ?? `faq-${slug(item.question) || index}`;

// ——— Component ———
export default function FAQAccordion({ items = [], categories, hotline, className, initialQuery = "" }: FaqProps) {
  const [query, setQuery] = useState(initialQuery);
  const [category, setCategory] = useState<string | "alle">("alle");
  const [openId, setOpenId] = useState<string | null>(null);

  // parse hash for deep-links
  useEffect(() => {
    const hash = typeof window !== "undefined" ? window.location.hash.replace("#", "") : "";
    if (hash) setOpenId(hash);
  }, []);

  const allCategories = useMemo(() => {
    const found = Array.from(new Set(items.map((i) => i.category).filter(Boolean))) as string[];
    if (categories && categories.length) {
      const extras = found.filter((c) => !categories.includes(c));
      return [...categories, ...extras];
    }
    return found;
  }, [items, categories]);

  const filtered = useMemo(() => {
    const q = query.trim().toLowerCase();
    const byCategory = (i: FaqItem) => (category === "alle" ? true : i.category === category);
    const byQuery = (i: FaqItem) => {
      if (!q) return true;
      const hay = (
        (i.question + " " + (typeof i.answer === "string" ? i.answer : "") + " " + (i.tags || []).join(" "))
      )
        .toLowerCase()
        .replace(/\s+/g, " ");
      return hay.includes(q);
    };
    return items
      .filter(byCategory)
      .filter(byQuery)
      .map((i) => ({
        item: i,
        score: (i.priority ?? 0) + (i.question.toLowerCase().includes(q) ? 1 : 0),
      }))
      .sort((a, b) => b.score - a.score)
      .map((x) => x.item);
  }, [items, query, category]);

  return (
    <div className={"w-full max-w-3xl mx-auto " + (className ?? "")}>
      <div className="mb-4 flex flex-col gap-3 sm:flex-row sm:items-center">
        <h2 className="text-2xl font-semibold tracking-tight">Häufige Fragen</h2>
        <div className="flex-1" />
        <div className="flex gap-2 w-full sm:w-auto">
          <input
            aria-label="Fragen durchsuchen"
            placeholder="Suche nach Thema, z. B. Rechnung, Karte, App…"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="w-full sm:w-80 rounded-xl border border-gray-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-gray-800"
          />
          <button
            className="rounded-xl border px-3 py-2 text-sm hover:bg-gray-50"
            onClick={() => { setQuery(""); setCategory("alle"); }}
            aria-label="Filter zurücksetzen"
          >
            Zurücksetzen
          </button>
        </div>
      </div>

      {allCategories.length > 0 && (
        <div className="mb-4 flex flex-wrap gap-2">
          <CategoryPill label="Alle" active={category === "alle"} onClick={() => setCategory("alle")} />
          {allCategories.map((c) => (
            <CategoryPill key={c} label={c} active={category === c} onClick={() => setCategory(c)} />
          ))}
        </div>
      )}

      {filtered.length === 0 && (
        <div className="rounded-xl border border-dashed p-6 text-center text-sm text-gray-600">
          Keine Treffer.
          {hotline ? (
            <>
              {" "}Bitte kontaktiere uns: {hotline.phone ? <a className="underline" href={`tel:${hotline.phone}`}>{hotline.phone}</a> : null}
              {hotline.email ? (
                <>
                  {hotline.phone ? " · " : null}
                  <a className="underline" href={`mailto:${hotline.email}`}>{hotline.email}</a>
                </>
              ) : null}
              {hotline.hours ? <div className="mt-1">Servicezeiten: {hotline.hours}</div> : null}
            </>
          ) : null}
        </div>
      )}

      <div className="flex flex-col gap-3">
        {filtered.map((it, idx) => {
          const id = toId(it, idx);
          const isOpen = openId === id;
          return (
            <Accordion
              key={id}
              id={id}
              summary={it.question}
              defaultExpanded={isOpen}
            >
              <div className="text-sm leading-relaxed text-gray-800">
                {it.answer}
                {it.updatedAt ? <div className="mt-2 text-xs text-gray-500">Aktualisiert: {fmtDate(it.updatedAt)}</div> : null}
              </div>
            </Accordion>
          );
        })}
      </div>

      {hotline && (
        <div className="mt-6 rounded-2xl border bg-gray-50 p-4">
          <div className="flex items-center gap-3">
            <span className="inline-flex h-9 w-9 items-center justify-center rounded-full border">☎️</span>
            <div>
              <div className="font-medium">Noch Fragen? Wir helfen gern.</div>
              <div className="text-sm text-gray-600">
                {hotline.hours ? `Servicezeiten: ${hotline.hours}` : ""}
                {hotline.note ? (hotline.hours ? " · " : "") + hotline.note : ""}
              </div>
            </div>
          </div>
          <div className="mt-3 flex flex-wrap gap-2">
            {hotline.phone && (
              <a href={`tel:${hotline.phone}`} className="rounded-xl border px-3 py-2 text-sm hover:bg-white">Anrufen ({hotline.phone})</a>
            )}
            {hotline.email && (
              <a href={`mailto:${hotline.email}`} className="rounded-xl border px-3 py-2 text-sm hover:bg-white">E-Mail schreiben</a>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

function CategoryPill({ label, active, onClick }: { label: string; active?: boolean; onClick?: () => void }) {
  return (
    <button
      onClick={onClick}
      className={
        "rounded-full border px-3 py-1 text-sm " +
        (active ? "bg-black text-white border-black" : "hover:bg-gray-50")
      }
      aria-pressed={active}
    >
      {label}
    </button>
  );
}

function fmtDate(d: string | Date) {
  const date = typeof d === "string" ? new Date(d) : d;
  if (Number.isNaN(date.getTime())) return "";
  return date.toLocaleDateString("de-DE", { year: "numeric", month: "2-digit", day: "2-digit" });
}

// ——— Example usage ———
export function ExampleFAQ() {
  const demoItems: FaqItem[] = [
    {
      question: "Ich kann die Ladesäule nicht starten – was kann ich tun?",
      answer: (
        <ul>
          <li>Überprüfe, ob deine Karte in der App als aktiv angezeigt wird.</li>
          <li>Trenne das Kabel, warte 30 Sekunden und versuche es erneut.</li>
          <li>Falls weiterhin Probleme bestehen: Nenne uns die <b>Stations-ID</b> vom Aufkleber.</li>
        </ul>
      ),
      category: "Laden",
      priority: 2,
      updatedAt: new Date(),
    },
    {
      question: "Wo finde ich meine Rechnungen?",
      answer: (
        <p>
          In der App unter <b>Profil → Rechnungen</b>. Zusätzlich senden wir sie per E‑Mail an die hinterlegte Adresse.
        </p>
      ),
      category: "Abrechnung",
      priority: 1,
    },
    {
      question: "Meine Karte ist verloren/defekt – wie sperre ich sie?",
      answer: (
        <p>
          Bitte sperre die Karte sofort in der App unter <b>Profil → Karten</b> und bestelle dort eine Ersatzkarte.
        </p>
      ),
      category: "Karte",
    },
  ];

  return (
    <FAQAccordion
      items={demoItems}
      categories={["Laden", "Abrechnung", "Karte"]}
      hotline={{ phone: "+49 421 1234567", email: "<EMAIL>", hours: "Mo–Fr 8–18 Uhr" }}
      className="my-6"
    />
  );
}
