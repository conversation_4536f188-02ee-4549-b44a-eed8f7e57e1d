import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";

export async function PATCH(
  req: Request,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id || session.user.role !== "ADMIN") {
    return new NextResponse("Forbidden", { status: 403 });
  }

  const body = await req.json();
  const { title, description, startsAt, endsAt, visible, legacy } = body; // 👈 legacy reinnehmen

  const updated = await prisma.achievementEvent.update({
    where: { id: params.id },
    data: {
      ...(title !== undefined && { title }),
      ...(description !== undefined && { description }),
      ...(startsAt !== undefined && {
        startsAt: startsAt ? new Date(startsAt) : null,
      }),
      ...(endsAt !== undefined && {
        endsAt: endsAt ? new Date(endsAt) : null,
      }),
      ...(visible !== undefined && { visible }),
      ...(legacy !== undefined && { legacy }), // 👈 legacy setzen
    },
  });

  return NextResponse.json(updated);
}
